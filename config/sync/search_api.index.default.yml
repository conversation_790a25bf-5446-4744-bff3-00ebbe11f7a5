uuid: f374eb46-25ea-40cc-ba7c-8f57f9ace110
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_sz_comments_count
    - field.storage.node.field_sz_likes_count
    - field.storage.node.field_sz_skills
    - field.storage.paragraph.field_sc_skill
    - search_api.server.default
  module:
    - bw_user_shortzzz_project
    - node
    - profile
    - search_api_solr
    - taxonomy
    - user
third_party_settings:
  search_api_solr:
    finalize: false
    commit_before_finalize: false
    commit_after_finalize: false
    debug_finalize: false
    highlighter:
      maxAnalyzedChars: 51200
      fragmenter: gap
      usePhraseHighlighter: true
      highlightMultiTerm: true
      preserveMulti: false
      regex:
        slop: 0.5
        pattern: blank
        maxAnalyzedChars: 10000
      highlight:
        mergeContiguous: false
        requireFieldMatch: false
        snippets: 3
        fragsize: 0
    mlt:
      mintf: 1
      mindf: 1
      maxdf: 0
      maxdfpct: 0
      minwl: 0
      maxwl: 0
      maxqt: 100
      maxntp: 2000
      boost: false
      interestingTerms: none
    term_modifiers:
      slop: 3
      fuzzy: 1
      fuzzy_analyzer: true
    advanced:
      index_prefix: ''
      collection: ''
      timezone: ''
    multilingual:
      limit_to_content_language: false
      include_language_independent: true
      use_language_undefined_as_fallback_language: false
      specific_languages:
        en: '0'
      use_universal_collation: false
id: default
name: Default
description: ''
read_only: false
field_settings:
  created:
    label: 'Authored on'
    datasource_id: 'entity:node'
    property_path: created
    type: date
    dependencies:
      module:
        - node
  date_created:
    label: 'Date created'
    datasource_id: 'entity:shortzzz_project'
    property_path: date_created
    type: date
    dependencies:
      module:
        - bw_user_shortzzz_project
  field_app_version:
    label: 'APP version'
    datasource_id: 'entity:node'
    property_path: field_app_version
    type: string
    dependencies:
      config:
        - field.storage.node.field_app_version
  hashtag_id:
    label: 'Term ID'
    datasource_id: 'entity:taxonomy_term'
    property_path: tid
    type: integer
    dependencies:
      module:
        - taxonomy
  hashtag_name:
    label: Name
    datasource_id: 'entity:taxonomy_term'
    property_path: name
    type: text
    boost: 2.0
    dependencies:
      module:
        - taxonomy
  hide_feed:
    label: 'Hide from feed'
    datasource_id: 'entity:node'
    property_path: field_hide_feed
    type: boolean
    dependencies:
      config:
        - field.storage.node.field_hide_feed
  node_id:
    label: ID
    datasource_id: 'entity:node'
    property_path: nid
    type: integer
    dependencies:
      module:
        - node
  node_status:
    label: Published
    datasource_id: 'entity:node'
    property_path: status
    type: boolean
    dependencies:
      module:
        - node
  node_sz_comments_count:
    label: 'Comments count'
    datasource_id: 'entity:node'
    property_path: field_sz_comments_count
    type: integer
    dependencies:
      config:
        - field.storage.node.field_sz_comments_count
  node_sz_hashtag_name:
    label: 'Hashtags » Taxonomy term » Name'
    datasource_id: 'entity:node'
    property_path: 'field_sz_hashtags:entity:name'
    type: text
    dependencies:
      config:
        - field.storage.node.field_sz_hashtags
      module:
        - taxonomy
  node_sz_likes_count:
    label: 'Likes count'
    datasource_id: 'entity:node'
    property_path: field_sz_likes_count
    type: integer
    dependencies:
      config:
        - field.storage.node.field_sz_likes_count
  node_title:
    label: Title
    datasource_id: 'entity:node'
    property_path: title
    type: text
    boost: 21.0
    dependencies:
      module:
        - node
  node_type:
    label: 'Content type'
    datasource_id: 'entity:node'
    property_path: type
    type: string
    dependencies:
      module:
        - node
  user_id:
    label: 'User ID'
    datasource_id: 'entity:user'
    property_path: uid
    type: integer
    dependencies:
      module:
        - user
  user_id_string:
    label: 'User ID'
    datasource_id: 'entity:user'
    property_path: uid
    type: string
    dependencies:
      module:
        - user
  user_name:
    label: Name
    datasource_id: 'entity:user'
    property_path: name
    type: text
    dependencies:
      module:
        - user
  user_roles:
    label: Roles
    datasource_id: 'entity:user'
    property_path: roles
    type: string
    dependencies:
      module:
        - user
  user_status:
    label: 'User status'
    datasource_id: 'entity:user'
    property_path: status
    type: boolean
    dependencies:
      module:
        - user
  user_views_count:
    label: 'Student profiles » Profile » Views count'
    datasource_id: 'entity:user'
    property_path: 'student_profiles:entity:field_s_views_count'
    type: integer
    dependencies:
      config:
        - field.storage.profile.field_s_views_count
      module:
        - profile
  uspj_hide_feed:
    label: 'Hide from feed'
    datasource_id: 'entity:shortzzz_project'
    property_path: hide_feed
    type: boolean
    dependencies:
      module:
        - bw_user_shortzzz_project
  uspj_id:
    label: ID
    datasource_id: 'entity:shortzzz_project'
    property_path: id
    type: integer
    dependencies:
      module:
        - bw_user_shortzzz_project
  uspj_shortzzz_id:
    label: 'Nano-framework ID'
    datasource_id: 'entity:shortzzz_project'
    property_path: sid
    type: integer
    dependencies:
      module:
        - bw_user_shortzzz_project
  uspj_status:
    label: Status
    datasource_id: 'entity:shortzzz_project'
    property_path: status
    type: string
    dependencies:
      module:
        - bw_user_shortzzz_project
  uspj_title:
    label: Title
    datasource_id: 'entity:shortzzz_project'
    property_path: title
    type: text
    dependencies:
      module:
        - bw_user_shortzzz_project
  uspj_user_id:
    label: 'User ID'
    datasource_id: 'entity:shortzzz_project'
    property_path: uid
    type: integer
    dependencies:
      module:
        - bw_user_shortzzz_project
  uspj_views_count:
    label: 'Views count'
    datasource_id: 'entity:shortzzz_project'
    property_path: views_count
    type: integer
    dependencies:
      module:
        - bw_user_shortzzz_project
datasource_settings:
  'entity:node':
    bundles:
      default: false
      selected:
        - alpha_gen
        - big5
        - coming_soon
        - shortzzz
        - ugc_by_brayn
    languages:
      default: true
      selected: {  }
  'entity:shortzzz_project': {  }
  'entity:taxonomy_term':
    bundles:
      default: false
      selected:
        - hashtag
    languages:
      default: true
      selected: {  }
  'entity:user':
    languages:
      default: true
      selected: {  }
processor_settings:
  add_url: {  }
  aggregated_field: {  }
  auto_aggregated_fulltext_field: {  }
  custom_value: {  }
  entity_type: {  }
  html_filter:
    weights:
      preprocess_index: -15
      preprocess_query: -15
    all_fields: true
    fields:
      - field_app_version
      - hashtag_name
      - node_sz_hashtag_name
      - node_title
      - node_type
      - user_id_string
      - user_name
      - user_roles
      - uspj_status
      - uspj_title
    title: true
    alt: true
    tags:
      b: 2
      h1: 5
      h2: 3
      h3: 2
      strong: 2
  language_with_fallback: {  }
  rendered_item: {  }
  solr_date_range:
    weights:
      preprocess_index: 0
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  delete_on_fail: true
  index_directly: true
  track_changes_in_references: true
server: default
