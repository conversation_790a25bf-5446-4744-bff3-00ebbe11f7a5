uuid: 843ba62b-6b8a-47f4-950e-fe85cad5df4b
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
  module:
    - node
    - taxonomy
    - user
_core:
  default_config_hash: YKgw0f77GEmCu6_6Om9Mbig0mON9JdfVuMxTtd0WQaI
id: taxonomy_term
label: 'Taxonomy term'
module: taxonomy
description: 'Content belonging to a certain taxonomy term.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields: {  }
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: 0
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        sticky:
          id: sticky
          table: taxonomy_index
          field: sticky
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: sticky
          exposed: false
        created:
          id: created
          table: taxonomy_index
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: created
          exposed: false
          granularity: second
      arguments:
        tid:
          id: tid
          table: taxonomy_index
          field: tid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          default_action: 'not found'
          exception:
            value: ''
            title_enable: false
            title: All
          title_enable: true
          title: '{{ arguments.tid }}'
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:taxonomy_term'
            fail: 'not found'
          validate_options:
            bundles: {  }
            access: true
            operation: view
            multiple: 0
          break_phrase: false
          add_table: false
          require_value: false
          reduce_duplicates: false
      filters:
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: language
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: taxonomy_index
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: 'entity:node'
        options:
          view_mode: teaser
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      link_display: page_1
      link_url: ''
      header:
        entity_taxonomy_term:
          id: entity_taxonomy_term
          table: views
          field: entity_taxonomy_term
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity
          empty: true
          target: '{{ raw_arguments.tid }}'
          view_mode: full
          tokenize: true
          bypass_access: false
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  feed_1:
    id: feed_1
    display_title: Feed
    display_plugin: feed
    position: 2
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 10
      style:
        type: rss
        options:
          grouping: {  }
          uses_fields: false
          description: ''
      row:
        type: node_rss
        options:
          relationship: none
          view_mode: default
      query:
        type: views_query
        options: {  }
      display_extenders: {  }
      path: taxonomy/term/%/feed
      displays:
        page_1: page_1
        default: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      query:
        type: views_query
        options: {  }
      display_extenders: {  }
      path: taxonomy/term/%
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
