_core:
  default_config_hash: 9rw2YgXiK9d42SeeSO8sPyCoxiG6rIlj64rJ8ocdEYU
client_key: ''
environment: ''
release: ''
log_levels:
  emergency: true
  alert: true
  critical: true
  error: true
  warning: true
  notice: false
  info: false
  debug: false
stack: true
timeout: 2.0
message_limit: 2048
trace: false
fatal_error_handler: true
fatal_error_handler_memory: 2560
javascript_error_handler: false
drush_error_handler: true
public_dsn: ''
ssl: verify_ssl
ca_cert: ''
ignored_channels: {  }
send_user_data: false
rate_limit: 0
send_request_body: false
request_tracing: false
traces_sample_rate: null
browser_traces_sample_rate: null
database_tracing: false
twig_tracing: false
auto_session_tracking: false
send_client_reports: false
drush_tracing: false
seckit_set_report_uri: false
send_monitoring_sensor_status_changes: false
404_tracing: false
show_report_dialog: false
error_embed_url: ''
tunnel: false
modules: false
cron_monitor_id: ''
profiles_sample_rate: null
http_compression: true
