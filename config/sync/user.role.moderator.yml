uuid: 92d3d7ea-14c8-4338-8b1e-d2124bd2b04c
langcode: en
status: true
dependencies:
  config:
    - filter.format.rich_text
    - media.type.audio
    - media.type.image
    - node.type.basic_page
    - node.type.shortzzz
    - rest.resource.bw_big_five
    - rest.resource.bw_user_branch_progress
    - taxonomy.vocabulary.hashtag
  module:
    - administerusersbyrole
    - bw_trophies
    - bw_vimeo
    - config_pages
    - file
    - filter
    - media
    - media_bulk_upload
    - node
    - paragraphs_type_permissions
    - profile
    - rest
    - system
    - taxonomy
    - toolbar
    - views_bulk_edit
id: moderator
label: Moderator
weight: -6
is_admin: null
permissions:
  - 'access administration pages'
  - 'access content overview'
  - 'access media overview'
  - 'access taxonomy overview'
  - 'access toolbar'
  - 'access trophies'
  - 'access user profiles'
  - 'access users overview'
  - 'administer nodes'
  - 'allow empty user mail'
  - 'bypass node access'
  - 'bypass paragraphs type content access'
  - 'cancel account'
  - 'cancel users by role'
  - 'change own username'
  - 'create audio media'
  - 'create basic_page content'
  - 'create image media'
  - 'create media'
  - 'create shortzzz content'
  - 'create terms in hashtag'
  - 'create users'
  - 'delete all revisions'
  - 'delete any audio media'
  - 'delete any audio media revisions'
  - 'delete any basic_page content'
  - 'delete any image media'
  - 'delete any image media revisions'
  - 'delete any media'
  - 'delete any shortzzz content'
  - 'delete basic_page revisions'
  - 'delete media'
  - 'delete own audio media'
  - 'delete own basic_page content'
  - 'delete own files'
  - 'delete own image media'
  - 'delete own shortzzz content'
  - 'delete shortzzz revisions'
  - 'delete terms in hashtag'
  - 'edit any audio media'
  - 'edit any basic_page content'
  - 'edit any image media'
  - 'edit any shortzzz content'
  - 'edit config_pages entity'
  - 'edit feeds config page entity'
  - 'edit lobby_banner config page entity'
  - 'edit market config page entity'
  - 'edit own audio media'
  - 'edit own basic_page content'
  - 'edit own image media'
  - 'edit own shortzzz content'
  - 'edit terms in hashtag'
  - 'edit users by role'
  - 'restful get bw_big_five'
  - 'restful get bw_user_branch_progress'
  - 'revert all revisions'
  - 'revert any audio media revisions'
  - 'revert any image media revisions'
  - 'revert basic_page revisions'
  - 'revert shortzzz revisions'
  - 'role-assign users by role'
  - 'update any media'
  - 'update media'
  - upload_vimeo_videos
  - 'use audio_upload bulk upload form'
  - 'use avatars bulk upload form'
  - 'use text format rich_text'
  - 'use views bulk edit'
  - 'view all media revisions'
  - 'view all revisions'
  - 'view any audio media revisions'
  - 'view any image media revisions'
  - 'view any student profile'
  - 'view basic_page revisions'
  - 'view config_pages entity'
  - 'view feeds config page entity'
  - 'view lobby_banner config page entity'
  - 'view market config page entity'
  - 'view own unpublished content'
  - 'view own unpublished media'
  - 'view shortzzz revisions'
  - 'view user email addresses'
  - 'view users by role'
