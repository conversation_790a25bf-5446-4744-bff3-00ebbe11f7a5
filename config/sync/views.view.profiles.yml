uuid: 6812fb31-e892-4ece-8ed0-3417627c85ae
langcode: en
status: true
dependencies:
  module:
    - profile
_core:
  default_config_hash: iDeLvK90ucO3Qc2NIH8ot0W1saUd7U_XThA3P1QvuMs
id: profiles
label: Profiles
module: views
description: ''
tag: ''
base_table: profile
base_field: profile_id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: Profiles
      fields:
        rendered_entity:
          id: rendered_entity
          table: profile
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: profile
          plugin_id: rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode: default
        operations:
          id: operations
          table: profile
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: profile
          plugin_id: entity_operations
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
      pager:
        type: none
        options:
          offset: 0
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'There are no profiles yet.'
            format: basic_html
          tokenize: false
      sorts:
        is_default:
          id: is_default
          table: profile
          field: is_default
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: profile
          entity_field: is_default
          plugin_id: standard
          order: DESC
          expose:
            label: ''
          exposed: false
        profile_id:
          id: profile_id
          table: profile
          field: profile_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: profile
          entity_field: profile_id
          plugin_id: standard
          order: DESC
          expose:
            label: ''
          exposed: false
      arguments:
        uid:
          id: uid
          table: profile
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: profile
          entity_field: uid
          plugin_id: entity_target_id
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: user
          default_argument_options:
            user: false
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
          target_entity_type_id: user
        type:
          id: type
          table: profile
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: profile
          entity_field: type
          plugin_id: string
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:profile_type'
            fail: 'not found'
          validate_options: {  }
          glossary: false
          limit: 0
          case: none
          path_case: none
          transform_dash: false
          break_phrase: false
        status:
          id: status
          table: profile
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: profile
          entity_field: status
          plugin_id: numeric
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters: {  }
      style:
        type: grid
        options:
          grouping: {  }
          columns: 4
          automatic_width: true
          alignment: horizontal
          row_class_custom: ''
          row_class_default: true
          col_class_custom: ''
          col_class_default: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
      tags: {  }
  user_page:
    id: user_page
    display_title: 'User page'
    display_plugin: embed
    position: 1
    display_options:
      display_description: ''
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
      tags: {  }
