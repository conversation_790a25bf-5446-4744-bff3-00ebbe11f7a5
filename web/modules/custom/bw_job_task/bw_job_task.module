<?php

/**
 * @file
 * Module-related hooks.
 */

use Drupal\bw_job_task\Value\JobTaskStatus;

/**
 * Implements hook_cron().
 */
function bw_job_task_cron() {
  /** @var \Drupal\Core\Database\Connection $connection */
  $connection = \Drupal::service('database');

  // Find stale jobs that are not failed or completed for more than 1 day.
  $stale_jobs = $connection->select('job_task', 'j')
    ->fields('j', ['job_id'])
    ->condition('created', (new \DateTime())->modify('- 1 day')->getTimestamp(), '<')
    ->condition('status', [JobTaskStatus::Failed->value, JobTaskStatus::Completed->value], 'NOT IN')
    ->orderBy('created', 'ASC')
    ->execute()
    ->fetchCol() ?: [];

  if (empty($stale_jobs)) {
    \Drupal::logger('bw_job_task')->notice('No stale jobs found.');
    return;
  }

  $now = (new \DateTime())->getTimestamp();
  // Mark stale jobs as failed.
  $connection->update('job_task')
    ->fields([
      'status' => JobTaskStatus::Failed->value,
      'updated' => $now,
      'error_msg' => json_encode([
        'status' => 'error',
        'code' => 'JOB_TASK_STALE',
        'message' => 'Job is stale and has been marked as failed.',
        'safe_message' => 'Job is stale and has been marked as failed.',
      ], JSON_UNESCAPED_SLASHES),
    ])
    ->condition('job_id', $stale_jobs, 'IN')
    ->execute();

  \Drupal::logger('bw_job_task')->notice('Marked @count stale jobs as failed.', ['@count' => count($stale_jobs)]);
}
