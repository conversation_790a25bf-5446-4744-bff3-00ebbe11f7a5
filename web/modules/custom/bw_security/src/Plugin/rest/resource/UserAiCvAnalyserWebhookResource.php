<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Drupal\bw_firebase\Service\FirebaseRemoteConfigClient;
use Drupal\bw_job_task\Service\JobTaskRepository;
use Drupal\bw_openai\Service\OpenAiService;
use Drupal\bw_rest_resources\Exception\BadRequestApiHttpException;
use Drupal\bw_rest_resources\Service\SsePusherService;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\KeyValueStore\KeyValueStoreExpirableInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Site\Settings;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\rest\Attribute\RestResource;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use StandardWebhooks\Exception\WebhookSigningException;
use StandardWebhooks\Exception\WebhookVerificationException;
use StandardWebhooks\Webhook;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;

/**
 * User AI CV analyser webhook resource.
 */
#[RestResource(
  id: "bw_user_ai_cv_analyser_webhook",
  label: new TranslatableMarkup("BW: User AI CV analyser webhook"),
  uri_paths: [
    "create" => "/api/{version}/user/ai-cv-analyser/webhook",
  ]
)]
final class UserAiCvAnalyserWebhookResource extends ResourceBase {

  /**
   * The OpenAI service.
   *
   * @var \Drupal\bw_openai\Service\OpenAiService
   */
  protected OpenAiService $openAiService;

  /**
   * The Firebase remote config client.
   *
   * @var \Drupal\bw_firebase\Service\FirebaseRemoteConfigClient
   */
  protected FirebaseRemoteConfigClient $firebaseRemoteConfigClient;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected AccountInterface $currentUser;

  /**
   * The job task repository.
   *
   * @var \Drupal\bw_job_task\Service\JobTaskRepository
   */
  protected JobTaskRepository $jobTaskRepository;

  /**
   * The key value expirable.
   *
   * @var \Drupal\Core\KeyValueStore\KeyValueStoreExpirableInterface
   */
  protected KeyValueStoreExpirableInterface $keyValueExpirable;

  /**
   * The SSE pusher service.
   *
   * @var \Drupal\bw_rest_resources\Service\SsePusherService
   */
  protected SsePusherService $sse;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $instance = parent::create($container, $configuration, $plugin_id, $plugin_definition);
    $instance->openAiService = $container->get('bw_openai.openai_service');
    $instance->firebaseRemoteConfigClient = $container->get('bw_firebase.client');
    $instance->entityTypeManager = $container->get('entity_type.manager');
    $instance->currentUser = $container->get('current_user');
    $instance->jobTaskRepository = $container->get('bw_job_task.repository');
    $instance->keyValueExpirable = $container->get('keyvalue.expirable')->get('ai_cv_analyser_background_jobs');
    $instance->sse = $container->get('bw_sse.service');
    return $instance;
  }

  /**
   * {@inheritdoc}
   *
   * Verify the webhook and process the response.
   */
  public function post(Request $request): ModifiedResourceResponse {
    $payload = $request->getContent() ?? '';

    if (empty($payload)) {
      throw new BadRequestApiHttpException('Invalid request data.', 'Invalid request data.', 'AI_CV_ANALYSER_INVALID_REQUEST_DATA');
    }

    try {
      $webhook = new Webhook(Settings::get('openai.webhook_secret'));
      $event = $webhook->verify($payload, $request->headers->all());
      $response_id = $event['data']['response']['id'] ?? '';
      $job_id = $this->keyValueExpirable->get($response_id);

      if (empty($job_id) || empty($response_id)) {
        throw new BadRequestApiHttpException('Invalid response id.', 'Invalid response id.', 'AI_CV_ANALYSER_INVALID_RESPONSE_ID');
      }

      $job = $this->jobTaskRepository->get($job_id);
      if (!$job || ($job->job_type !== 'ai_cv_analyser')) {
        throw new BadRequestApiHttpException('Invalid job id.', 'Invalid job id.', 'AI_CV_ANALYSER_INVALID_JOB_ID');
      }

      if ($event['type'] !== 'response.completed') {
        $this->logger->error('Invalid webhook event type: @type', ['@type' => $event['type']]);
        $this->jobTaskRepository->incrementFailed($job_id, 'CHANGEME');
        $this->jobTaskRepository->failIfAnyFailed($job_id);
        $this->sse->push('ai_cv_analyser', $job->uid, $job_id, ['type' => 'failed', 'error_msg' => 'CHANGEME']);
        throw new BadRequestApiHttpException('Invalid webhook event type.', 'Invalid webhook event type.', 'AI_CV_ANALYSER_INVALID_WEBHOOK_EVENT_TYPE');
      }

      $response = $this->openAiService->getClient()->responses()->retrieve($response_id);
      $json = $response->outputText ?? '';
      $data = json_decode($json, TRUE) ?? [];

      if (json_last_error() !== JSON_ERROR_NONE) {
        $this->logger->error('Error performing AI CV analyzer: @message', ['@message' => 'Model output was not valid JSON: ' . json_last_error_msg() . "\n\n" . $json]);
        throw new HttpException(500, 'Model output was not valid JSON.');
      }

      // Clean up to avoid storing files forever.
      $this->openAiService->getClient()->vectorStores()->delete(vectorStoreId: $vector_store->id);
      $this->openAiService->getClient()->files()->delete(file: $uploaded_file->id);

      // Check the status.
      if (empty($data['status']) || $data['status'] !== 'success') {
        $this->logger->error('Error performing AI CV analyzer: @message', ['@message' => 'Model output was not successful: ' . json_encode($data)]);
        throw new BadRequestApiHttpException('Error performing AI CV analyzer.', $data['payload']['error']['message'] ?? 'Error performing AI CV analyzer.', $data['payload']['error']['code'] ?? 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      }

      if (empty($data['payload'])) {
        $this->logger->error('Error performing AI CV analyzer: @message', ['@message' => 'Model output was not successful: ' . json_encode($data)]);
        throw new BadRequestApiHttpException('Error performing AI CV analyzer.', 'Error performing AI CV analyzer.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      }

      $cv_profile = $this->entityTypeManager->getStorage('profile')
        ->loadByProperties([
          'type' => 'cv',
          'uid' => $this->currentUser->id(),
        ]);
      /** @var \Drupal\profile\Entity\Profile $cv_profile */
      $cv_profile = reset($cv_profile);

      if (!$cv_profile) {
        /** @var \Drupal\profile\Entity\Profile $cv_profile */
        $cv_profile = $this->entityTypeManager->getStorage('profile')->create([
          'type' => 'cv',
          'uid' => $this->currentUser->id(),
        ]);
      }

      $cv_profile->set('field_ai_cv_analyser', serialize($data['payload']));
      $cv_profile->save();
    }
    catch (WebhookVerificationException $e) {
      throw new BadRequestApiHttpException('Invalid webhook signature.', 'Invalid webhook signature.', 'AI_CV_ANALYSER_INVALID_WEBHOOK_SIGNATURE');
    }
    catch (WebhookSigningException $e) {
      throw new BadRequestApiHttpException('Invalid webhook signature.', 'Invalid webhook signature.', 'AI_CV_ANALYSER_INVALID_WEBHOOK_SIGNATURE');
    }
    catch (BadRequestApiHttpException $e) {
      throw $e;
    }
    catch (\Exception $e) {
      $this->logger->error('Error analyzing CV: @message', ['@message' => $e->getMessage()]);
      throw new HttpException(500, 'Error analyzing CV.');
    }

    return new ModifiedResourceResponse();
  }

}
