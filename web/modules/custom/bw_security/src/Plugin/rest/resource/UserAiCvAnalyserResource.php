<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Drupal\bw_firebase\Service\FirebaseRemoteConfigClient;
use Drupal\bw_job_task\Service\JobTaskRepository;
use Drupal\bw_openai\Service\OpenAiService;
use Drupal\bw_rest_resources\Exception\BadRequestApiHttpException;
use Drupal\bw_rest_resources\Exception\NotFoundApiHttpException;
use Drupal\bw_rest_resources\Exception\UnprocessableEntityApiHttpException;
use Drupal\bw_rest_resources\Service\SsePusherService;
use Drupal\Core\Database\Connection;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\KeyValueStore\KeyValueStoreExpirableInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\file\Entity\File;
use Drupal\file\FileInterface;
use Drupal\file\Plugin\rest\resource\FileUploadResource;
use Drupal\rest\Attribute\RestResource;
use Drupal\rest\ModifiedResourceResponse;
use Ramsey\Uuid\Uuid;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;

/**
 * User AI CV analyser resource.
 */
#[RestResource(
  id: "bw_user_ai_cv_analyser",
  label: new TranslatableMarkup("BW: User AI CV analyser"),
  serialization_class: File::class,
  uri_paths: [
    "create" => "/api/{version}/user/ai-cv-analyser",
  ]
)]
final class UserAiCvAnalyserResource extends FileUploadResource {

  /**
   * The OpenAI service.
   *
   * @var \Drupal\bw_openai\Service\OpenAiService
   */
  protected OpenAiService $openAiService;

  /**
   * The Firebase remote config client.
   *
   * @var \Drupal\bw_firebase\Service\FirebaseRemoteConfigClient
   */
  protected FirebaseRemoteConfigClient $firebaseRemoteConfigClient;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected AccountInterface $currentUser;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  protected Connection $connection;

  /**
   * The job task repository.
   *
   * @var \Drupal\bw_job_task\Service\JobTaskRepository
   */
  protected JobTaskRepository $jobTaskRepository;

  /**
   * The key value expirable.
   *
   * @var \Drupal\Core\KeyValueStore\KeyValueStoreExpirableInterface
   */
  protected KeyValueStoreExpirableInterface $keyValueExpirable;

  /**
   * The SSE pusher service.
   *
   * @var \Drupal\bw_rest_resources\Service\SsePusherService
   */
  protected SsePusherService $sse;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $instance = parent::create($container, $configuration, $plugin_id, $plugin_definition);
    $instance->openAiService = $container->get('bw_openai.openai_service');
    $instance->firebaseRemoteConfigClient = $container->get('bw_firebase.client');
    $instance->entityTypeManager = $container->get('entity_type.manager');
    $instance->currentUser = $container->get('current_user');
    $instance->connection = $container->get('database');
    $instance->jobTaskRepository = $container->get('bw_job_task.repository');
    $instance->keyValueExpirable = $container->get('keyvalue.expirable')->get('ai_cv_analyser_background_jobs');
    $instance->sse = $container->get('bw_rest_resources.sse_pusher');
    return $instance;
  }

  /**
   * {@inheritdoc}
   *
   * Use the file upload resource.
   */
  public function post(Request $request, $entity_type_id = 'profile', $bundle = 'cv', $field_name = 'field_original_cv', string $version = 'v1'): ModifiedResourceResponse {
    $response = parent::post($request, $entity_type_id, $bundle, $field_name);

    if (!(($file = $response->getResponseData())) instanceof FileInterface) {
      throw new NotFoundApiHttpException('Error fetching file information.', "Didn't catch the file this time. Let's give it another go.", 'AI_CV_ANALYSER_FILE_NOT_FOUND');
    }

    try {
      // @todo add version here.
      $prompt = $this->firebaseRemoteConfigClient->getParameterValue('ai_cv_analyzer_prompt_' . $version) ?? 'Resume analyser';
      $model = $this->firebaseRemoteConfigClient->getParameterValue('ai_cv_analyzer_model_' . $version) ?? 'gpt-5-mini';
      if ($version === 'v1') {
        $json_schema = [
          'type' => 'object',
          'additionalProperties' => FALSE,
          'properties' => [
            'insight_1' => ['type' => 'string'],
            'insight_2' => ['type' => 'string'],
            'feedback_1' => ['type' => 'string'],
            'feedback_2' => ['type' => 'string'],
            'feedback_3' => ['type' => 'string'],
            'tip_1' => ['type' => 'string'],
            'tip_2' => ['type' => 'string'],
            'tip_3' => ['type' => 'string'],
          ],
          'required' => [
            'insight_1',
            'insight_2',
            'feedback_1',
            'feedback_2',
            'feedback_3',
            'tip_1',
            'tip_2',
            'tip_3',
          ],
        ];
      } else {
        $json_schema = [
          'type' => 'object',
          'additionalProperties' => FALSE,
          'properties' => [
            'version' => [
              'type' => 'string',
              'description' => "Simulator version, e.g. '0.12.1'.",
            ],

            'status' => [
              'type' => 'string',
              'description' => "Status of the response, e.g. 'success' or 'error'.",
            ],

            'payload' => [
              'anyOf' => [
                [
                  'type' => 'object',
                  'additionalProperties' => FALSE,
                  'properties' => [
                    'overall' => [
                      'type' => 'object',
                      'additionalProperties' => FALSE,
                      'properties' => [
                        'sus_score' => ['type' => 'integer', 'minimum' => 0, 'maximum' => 100],
                        'grade_name' => ['type' => 'string'],
                        'grade_tagline' => ['type' => 'string'],
                        'prologue' => [
                          'type' => 'string',
                          'description' => 'Concise 20-30 word coaching paragraph; single paragraph.',
                        ],
                      ],
                      'required' => ['sus_score', 'grade_name', 'grade_tagline', 'prologue'],
                    ],

                    'sections' => [
                      'type' => 'array',
                      'description' => 'Up to nine sections in canonical order. Omit any section that cannot be confidently inferred.',
                      'items' => [
                        'type' => 'object',
                        'additionalProperties' => FALSE,
                        'properties' => [
                          'title' => ['type' => 'string'],
                          'narrative' => [
                            'type' => 'string',
                            'description' => 'One short, single-sentence micro-narrative (~20-30 words).',
                          ],
                          'lines' => [
                            'type' => 'array',
                            'description' => 'Labeled lines. Omit any line that cannot be confidently inferred.',
                            'items' => [
                              'type' => 'object',
                              'additionalProperties' => FALSE,
                              'properties' => [
                                'label' => ['type' => 'string'],
                                'text'  => ['type' => 'string'],
                              ],
                              'required' => ['label', 'text'],
                            ],
                          ],
                        ],
                        'required' => ['title', 'narrative', 'lines'],
                      ],
                    ],

                    'component_scores' => [
                      'type' => 'object',
                      'additionalProperties' => FALSE,
                      'properties' => [
                        'skills_reactor' => ['type' => 'integer'],
                        'impact_cannons' => ['type' => 'integer'],
                        'ats'            => ['type' => 'integer'],
                        'adaptive_armor' => ['type' => 'integer'],
                        'proof'          => ['type' => 'integer'],
                        'platforms'      => ['type' => 'integer'],
                        'verification'   => ['type' => 'integer'],
                        'eurythmia'      => ['type' => 'integer'],
                        'design'         => ['type' => 'integer'],
                      ],
                      'required' => [
                        'skills_reactor',
                        'impact_cannons',
                        'ats',
                        'adaptive_armor',
                        'proof',
                        'platforms',
                        'verification',
                        'eurythmia',
                        'design',
                      ],
                    ],
                  ],
                  'required' => ['overall', 'sections', 'component_scores'],
                ],
                [
                  'type' => 'object',
                  'additionalProperties' => FALSE,
                  'properties' => [
                    'error' => [
                      'type' => 'object',
                      'additionalProperties' => FALSE,
                      'properties' => [
                        'code' => ['type' => 'string'],
                        'message' => ['type' => 'string'],
                        'hint'    => ['type' => 'string'],
                      ],
                      'required' => ['code', 'message', 'hint'],
                    ],
                  ],
                  'required' => ['error'],
                ],
              ],
            ],
          ],
          'required' => ['version', 'status', 'payload'],
        ];
      }

      // Upload the CV to OpenAI.
      $uploaded_file = $this->openAiService->getClient()->files()->upload([
        'file' => fopen($file->getFileUri(), 'r'),
        'purpose' => 'assistants',
      ]);

      // Call the Responses API, with strict JSON schema.
      $response = $this->openAiService->getClient()->responses()->create([
        'model' => $model,
        'background' => $version === 'v1' ? FALSE : TRUE,
        'input' => [
          [
            'role' => 'system',
            'content' => $prompt,
          ],
          [
            'role' => 'user',
            'content' => [
              [
                'type' => 'input_file',
                'file_id' => $uploaded_file->id,
              ],
            ],
          ],
        ],
        'text' => [
          'format' => [
            'type' => 'json_schema',
            'name' => 'CvAnalyser',
            'strict' => TRUE,
            'schema' => $json_schema,
          ],
        ],
      ]);

      $file->delete();

      // @todo: Remove when v1 is dismissed.
      if ($version === 'v1') {
        $json = $response->outputText ?? '';
        $data = json_decode($json, TRUE) ?? [];

        if (json_last_error() !== JSON_ERROR_NONE) {
          $this->logger->error('Error performing AI CV analyzer: @message', ['@message' => 'Model output was not valid JSON: ' . json_last_error_msg() . "\n\n" . $json]);
          throw new BadRequestApiHttpException('Error performing AI CV analyzer.', "Hmm, something's off. Want to try that again?", 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
        }

        $cv_profile = $this->entityTypeManager->getStorage('profile')
          ->loadByProperties([
            'type' => 'cv',
            'uid' => $this->currentUser->id(),
          ]);
        /** @var \Drupal\profile\Entity\Profile $cv_profile */
        $cv_profile = reset($cv_profile);

        if (!$cv_profile) {
          /** @var \Drupal\profile\Entity\Profile $cv_profile */
          $cv_profile = $this->entityTypeManager->getStorage('profile')->create([
            'type' => 'cv',
            'uid' => $this->currentUser->id(),
          ]);
        }

        $cv_profile->set('field_ai_cv_analyser', serialize($data));
        $cv_profile->save();

        $this->openAiService->getClient()->files()->delete(file: $uploaded_file->id);

        return new ModifiedResourceResponse($data, 200);
      }
    }
    catch (\Exception $e) {
      try {
        $file->delete();
      }
      catch (\Exception) {
      }

      // @todo: Remove when v1 is dismissed.
      if ($e instanceof BadRequestApiHttpException) {
        throw $e;
      }

      $this->logger->error('Error analyzing CV: @message', ['@message' => $e->getMessage()]);
      throw new UnprocessableEntityApiHttpException('Open AI failed to initialize CV analysis.', "Hmm, something's off. Want to try that again?", 'AI_CV_ANALYSER_OPEN_AI_INITIALIZATION_ERROR');
    }

    $transaction = $this->connection->startTransaction();
    try {
      $job_id = Uuid::uuid4()->toString();
      $uid = $this->currentUser->id();
      $this->jobTaskRepository->create($job_id, 'ai_cv_analyser', $uid, 1, [
        'response_id' => $response->id,
        'file_name' => $file->label(),
        'uploaded_file_id' => $uploaded_file->id,
      ]);

      // Store the job ID in key value expirable.
      $this->keyValueExpirable->set($response->id, $job_id, 86400);

      $this->sse->push('ai_cv_analyser', $uid, $job_id, ['type' => 'progress', 'progress' => 0]);
    }
    catch (\Throwable $e) {
      $transaction->rollBack();
      $this->logger->error('Error creating job task: @message', ['@message' => $e->getMessage()]);
      throw new HttpException(500, 'Error creating job task.');
    }

    return new ModifiedResourceResponse(['job_id' => $job_id], 202);
  }

}
