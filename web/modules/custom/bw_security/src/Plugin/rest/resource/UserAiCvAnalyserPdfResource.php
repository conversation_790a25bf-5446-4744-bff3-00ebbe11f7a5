<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Dr<PERSON>al\bw_pdf\Service\DomPdfService;
use Drupal\bw_rest_resources\Exception\NotFoundApiHttpException;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON>upal\Core\Extension\ExtensionPathResolver;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\rest\Attribute\RestResource;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * User AI CV analyser PDF resource.
 */
#[RestResource(
  id: "bw_user_ai_cv_analyser_pdf",
  label: new TranslatableMarkup("BW: User AI CV analyser PDF"),
  uri_paths: [
    "canonical" => "/api/{version}/user/ai-cv-analyser/pdf",
  ]
)]
final class UserAiCvAnalyserPdfResource extends ResourceBase {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The PDF service.
   *
   * @var \Drupal\bw_pdf\Service\DomPdfService
   */
  private DomPdfService $pdfService;

  /**
   * The extension path resolver.
   *
   * @var \Drupal\Core\Extension\ExtensionPathResolver
   */
  private ExtensionPathResolver $extensionPathResolver;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\bw_pdf\Service\DomPdfService $pdf_service
   *   The PDF service.
   * @param \Drupal\Core\Extension\ExtensionPathResolver $extensionPathResolver
   *   Resolves module and theme paths.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    AccountInterface $current_user,
    EntityTypeManagerInterface $entity_type_manager,
    DomPdfService $pdf_service,
    ExtensionPathResolver $extensionPathResolver,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->currentUser = $current_user;
    $this->entityTypeManager = $entity_type_manager;
    $this->pdfService = $pdf_service;
    $this->extensionPathResolver = $extensionPathResolver;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('current_user'),
      $container->get('entity_type.manager'),
      $container->get('bw_pdf.service'),
      $container->get('extension.path.resolver'),
    );
  }

  /**
   * Get the AI CV analyser data in PDF format.
   *
   * @return \Symfony\Component\HttpFoundation\Response
   *   The HTTP response object.
   */
  public function get(string $version = 'v1'): Response {
    /** @var \Drupal\user\Entity\User $user */
    $user = $this->entityTypeManager->getStorage('user')->load($this->currentUser->id());
    /** @var \Drupal\profile\Entity\Profile $cv_profile */
    $cv_profile = $this->entityTypeManager->getStorage('profile')
      ->loadByProperties([
        'type' => 'cv',
        'uid' => $user->id(),
      ]);
    $cv_profile = reset($cv_profile);

    if (!$user->isActive() || !$cv_profile) {
      throw new NotFoundApiHttpException('Data for the user could not be found.', "We can't find your data. Want to start fresh?", 'AI_CV_ANALYSER_USER_DATA_NOT_FOUND');
    }

    $data = [];
    if ($cv_profile->hasField('field_ai_cv_analyser') && !$cv_profile->field_ai_cv_analyser->isEmpty()) {
      $data = unserialize($cv_profile->field_ai_cv_analyser->value, ['allowed_classes' => FALSE]);
    }

    if (empty($data)) {
      throw new NotFoundApiHttpException('Data for the user could not be found.', "We can't find your data. Want to start fresh?", 'AI_CV_ANALYSER_USER_DATA_NOT_FOUND');
    }

    $overlay_img = $this->extensionPathResolver
      ->getPath('module', 'bw_pdf') . '/assets/images/cv-analyser-background.png';
    $base64_image = '';
    if (is_file($overlay_img)) {
      $base64_image = 'data:image/png;base64,' . base64_encode(file_get_contents($overlay_img));
    }

    if ($version === 'v1') {
      $inline_css = "
        body {
          padding: 200px 90px 120px;
        }

        .overlay {
          background-image: url(\"{$base64_image}\");
          background-repeat: no-repeat;
          background-size: cover;
          background-position: centre centre;
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: -1;
        }

        .pdf-header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          height: 180px;
          z-index: 1;
        }

        .pdf-footer {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          height: 120px;
          z-index: 1;
        }

        .pdf-body {
          color: #fff;
        }

        ul {
          padding: 0;
          margin: 0;
        }

        ul > li {
          margin: 0;
          margin-bottom: 10px;
          padding: 0;
          color: #fff;
        }
      ";
    } else {
      $inline_css = "
        body {
          padding: 110px 80px 110px;
        }

        .overlay {
          background-image: url(\"{$base64_image}\");
          background-repeat: no-repeat;
          background-size: cover;
          background-position: centre centre;
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: -1;
        }

        .pdf-header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          height: 90px;
          z-index: 1;
        }

        .pdf-footer {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          height: 110px;
          z-index: 1;
        }

        .pdf-body {
          color: #fff;
          font-size: 10px;
          line-height: 120%;
        }

        .heading {
          font-size: 14px;
          font-weight: 700;
        }

        .grade-tagline {
          font-size: 12px;
          font-weight: 700;
          color: rgba(121, 220, 245, 1);
          margin-bottom: 4px;
        }

        .prologue {
          margin-bottom: 4px;
        }

        .section-heading {
          font-size: 11px;
          font-weight: 700;
          color: rgba(213, 242, 14, 1);
          margin-bottom: 4px;
        }

        .section-lines {
          margin-bottom: 4px;
        }

        .section-line-heading {
          font-size: 11px;
          font-weight: 700;
          color: rgba(121, 220, 245, 1);
        }
      ";
    }
    $build['#css'] = $inline_css;

    if ($version === 'v1') {
      $build['#content'] = [
        '#theme' => 'item_list',
        '#list_type' => 'ul',
        '#items' => $data,
      ];
    } else {
      $build['#content'] = [
        '#type' => 'container',
      ];

      if (!empty($data['overall'])) {
        $overall_markup = '';
        if (!empty($data['overall']['sus_score']) && !empty($data['overall']['grade_name'])) {
          $overall_markup .= '<div class="heading">SCORE: ' . $data['overall']['sus_score'] . ' ' . $data['overall']['grade_name'] . '</div>';
        }

        if (!empty($data['overall']['grade_tagline'])) {
          $overall_markup .= '<div class="grade-tagline">' . $data['overall']['grade_tagline'] . '</div>';
        }

        if (!empty($data['overall']['prologue'])) {
          $overall_markup .= '<div class="prologue">' . $data['overall']['prologue'] . '</div>';
        }

        $build['#content']['overall'] = [
          '#type' => 'markup',
          '#markup' => $overall_markup,
        ];
      }

      if (!empty($data['sections'])) {
        foreach ($data['sections'] as $section) {
          $section_markup = '';
          if (!empty($section['title'])) {
            $section_markup .= '<div class="section-heading">' . $section['title'] . '</div>';
          }

          if (!empty($section['narrative'])) {
            $section_markup .= '<div>' . $section['narrative'] . '</div>';
          }

          if (!empty($section['lines'])) {
            $section_markup .= '<div class="section-lines">';
            foreach ($section['lines'] as $line) {
              if (empty($line['label']) || empty($line['text'])) {
                continue;
              }

              $section_markup .= '<div class="section-line-heading">' . $line['label'] . '</div><div>' . $line['text'] . '</div>';
            }
            $section_markup .= '</div>';
          }

          $build['#content']['sections'][] = [
            '#type' => 'markup',
            '#markup' => $section_markup,
          ];
        }
      }
    }

    try {
      return $this->pdfService->render($build);
    }
    catch (\Exception $e) {
      $this->logger->error('Failed to generate PDF: @message', ['@message' => $e->getMessage()]);
      throw new NotFoundApiHttpException('Failed to generate PDF.', "Couldn't create your proof doc. Want to retry?", 'AI_CV_ANALYSER_PDF_GENERATION_FAILED');
    }
  }

}
