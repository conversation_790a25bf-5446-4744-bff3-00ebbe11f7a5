<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Dr<PERSON>al\bw_job_task\Service\JobTaskRepository;
use Drupal\bw_rest_resources\Exception\NotFoundApiHttpException;
use Drupal\Core\Session\AccountInterface;
use <PERSON><PERSON>al\redis\ClientFactory;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * Base class for SSE job task events resources.
 */
abstract class EventsStreamResourceBase extends ResourceBase {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected AccountInterface $currentUser;

  /**
   * The job task repository.
   *
   * @var \Drupal\bw_job_task\Service\JobTaskRepository
   */
  protected JobTaskRepository $jobTaskRepository;

  /**
   * The Redis client.
   *
   * @var \Drupal\redis\ClientFactory
   */
  protected ClientFactory $redis;

  // Hard cap: exit by this time, protects PHP workers.
  protected const MAX_RUNTIME_SEC = 300;
  // No messages for this long → exit (client can reconnect)
  protected const IDLE_TIMEOUT_SEC = 240;
  // Comment pings keep proxies from buffering/closing.
  protected const HEARTBEAT_SEC = 15;
  // Short blocking wait per loop.
  protected const BRPOP_TIMEOUT_SEC = 5;
  // Safety cap on number of events pushed per connection.
  protected const MAX_EVENTS = 1000;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\bw_job_task\Service\JobTaskRepository $job_task_repository
   *   The job task repository.
   * @param \Drupal\redis\ClientFactory $redis
   *   The Redis client factory.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    AccountInterface $current_user,
    JobTaskRepository $job_task_repository,
    ClientFactory $redis,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->currentUser = $current_user;
    $this->jobTaskRepository = $job_task_repository;
    $this->redis = $redis;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('current_user'),
      $container->get('bw_job_task.repository'),
      $container->get('redis.factory'),
    );
  }

  /**
   * Get the job type for validation.
   *
   * @return string
   *   The job type.
   */
  abstract protected function getJobType(): string;

  /**
   * Throw the job not found exception.
   *
   * @throws \Drupal\bw_rest_resources\Exception\NotFoundApiHttpException
   *   The job not found exception.
   */
  abstract protected function throwJobNotFoundException(): NotFoundApiHttpException;

  /**
   * SSE stream of job events for the current user and job.
   *
   * @param string $job_id
   *   The job id.
   *
   * @return \Symfony\Component\HttpFoundation\StreamedResponse
   *   The HTTP response object.
   */
  public function get(string $job_id): StreamedResponse {
    $job = $this->jobTaskRepository->get($job_id);
    if (!$job || ($job->uid !== $this->currentUser->id()) || ($job->job_type !== $this->getJobType())) {
      throw $this->throwJobNotFoundException();
    }

    // PhpRedis or Predis\Client.
    $client = $this->redis->getClient();
    $uid = (string) $this->currentUser->id();
    $key = sprintf('sse:%s:%s:%s', $this->getJobType(), $uid, $job_id);

    $response = new StreamedResponse(function () use ($client, $key) {
      // Keep the worker under control.
      @set_time_limit(static::MAX_RUNTIME_SEC + 5);
      @ini_set('output_buffering', 'off');
      @ini_set('zlib.output_compression', '0');
      while (ob_get_level() > 0) {
        @ob_end_flush();
      }

      header('Content-Type: text/event-stream');
      header('Cache-Control: no-cache, no-transform');
      header('X-Accel-Buffering: no');
      header('Connection: keep-alive');

      $flush = static function (): void {
        @flush();
        if (function_exists('fastcgi_finish_request')) {
          /* do not call here; we want streaming */
        }
      };

      // Open event.
      echo "event: open\n";
      echo "data: {\"ok\":true}\n\n";
      $flush();

      $start = microtime(TRUE);
      $lastBeat = microtime(TRUE);
      $lastAny = microtime(TRUE);
      $sentCount = 0;

      // Replay backlog (non-blocking).
      try {
        $backlog = $client->lrange($key, 0, -1);

        foreach (array_reverse($backlog) as $payload) {
          echo "event: message\n";
          echo "data: $payload\n\n";
          $flush();
          $sentCount++;
          $lastAny = microtime(TRUE);
          if ($sentCount >= static::MAX_EVENTS) {
            break;
          }
          if (connection_aborted()) {
            return;
          }
        }
      }
      catch (\Throwable $e) {
        // If Redis hiccups, end gracefully; client will reconnect.
        echo "event: message\n";
        echo "data: {\"type\":\"warning\",\"reason\":\"backlog-unavailable\"}\n\n";
        $flush();
      }

      // Main loop.
      $running = TRUE;
      while ($running) {
        // Hard runtime cap.
        if ((microtime(TRUE) - $start) >= static::MAX_RUNTIME_SEC) {
          echo "event: message\n";
          echo "data: {\"type\":\"end\",\"reason\":\"time-limit\"}\n\n";
          $flush();
          break;
        }

        // Idle timeout (no events for too long)
        if ((microtime(TRUE) - $lastAny) >= static::IDLE_TIMEOUT_SEC) {
          echo "event: message\n";
          echo "data: {\"type\":\"end\",\"reason\":\"idle-timeout\"}\n\n";
          $flush();
          break;
        }

        // Blocking pop with small timeout.
        try {
          $item = $client->brPop([$key], static::BRPOP_TIMEOUT_SEC);
        }
        catch (\Throwable $e) {
          // Transient Redis error: send warning and back off a little.
          echo "event: message\n";
          echo "data: {\"type\":\"warning\",\"reason\":\"redis-error\"}\n\n";
          $flush();
          // 200ms
          usleep(200_000);
          $item = NULL;
        }

        if ($item) {
          $payload = is_array($item) ? $item[1] : $item;
          echo "event: message\n";
          echo "data: $payload\n\n";
          $flush();

          $sentCount++;
          $lastAny = microtime(TRUE);

          if ($sentCount >= static::MAX_EVENTS) {
            echo "event: message\n";
            echo "data: {\"type\":\"end\",\"reason\":\"max-events\"}\n\n";
            $flush();
            break;
          }

          $decoded = json_decode($payload, TRUE);
          if (isset($decoded['type']) && in_array($decoded['type'], ['completed', 'failed'], TRUE)) {
            // End naturally on terminal state.
            $running = FALSE;
          }
        }

        // Heartbeat.
        if ((microtime(TRUE) - $lastBeat) >= static::HEARTBEAT_SEC) {
          echo ": keep-alive\n\n";
          $flush();
          $lastBeat = microtime(TRUE);
        }

        // Client disconnected.
        if (connection_aborted()) {
          break;
        }
      }
    });

    $response->headers->set('Content-Type', 'text/event-stream');
    $response->headers->set('Cache-Control', 'no-cache, private, no-transform');
    $response->headers->set('X-Accel-Buffering', 'no');

    return $response;
  }

}
