<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Dr<PERSON>al\bw_job_task\JobTaskSerializer;
use Dr<PERSON>al\bw_job_task\Service\JobTaskRepository;
use Drupal\bw_rest_resources\Exception\NotFoundApiHttpException;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\rest\Attribute\RestResource;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Get the AI CV analyser job task status.
 */
#[RestResource(
  id: "bw_user_ai_cv_analyser_job_task",
  label: new TranslatableMarkup("BW: User AI CV analyser job task"),
  uri_paths: [
    "canonical" => "/api/{version}/user/ai-cv-analyser/{job_id}",
  ]
)]
final class UserAiCvAnalyserJobTaskResource extends ResourceBase {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * The job task repository.
   *
   * @var \Drupal\bw_job_task\Service\JobTaskRepository
   */
  private JobTaskRepository $jobTaskRepository;

  /**
   * The job task serializer.
   *
   * @var \Drupal\bw_job_task\JobTaskSerializer
   */
  private JobTaskSerializer $jobTaskSerializer;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\bw_job_task\Service\JobTaskRepository $job_task_repository
   *   The job task repository.
   * @param \Drupal\bw_job_task\JobTaskSerializer $job_task_serializer
   *   The job task serializer.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    AccountInterface $current_user,
    JobTaskRepository $job_task_repository,
    JobTaskSerializer $job_task_serializer,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->currentUser = $current_user;
    $this->jobTaskRepository = $job_task_repository;
    $this->jobTaskSerializer = $job_task_serializer;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('current_user'),
      $container->get('bw_job_task.repository'),
      $container->get('bw_job_task.serializer.job_task'),
    );
  }

  /**
   * Get the AI CV analyser job task status.
   *
   * @param string $job_id
   *   The job id.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function get(string $job_id): ModifiedResourceResponse {
    $job = $this->jobTaskRepository->get($job_id);
    if (!$job || ($job->uid !== $this->currentUser->id()) || ($job->job_type !== 'ai_cv_analyser')) {
      throw new NotFoundApiHttpException('The AI CV analyser job could not be found.', 'Job not found.', 'AI_CV_ANALYSER_JOB_NOT_FOUND');
    }

    return new ModifiedResourceResponse($this->jobTaskSerializer->deserialize($job), 200);
  }

}
