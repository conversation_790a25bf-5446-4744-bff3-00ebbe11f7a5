<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Drupal\bw_firebase\Service\FirebaseRemoteConfigClient;
use Drupal\bw_job_task\Service\JobTaskRepository;
use Drupal\bw_openai\Service\OpenAiService;
use Drupal\bw_rest_resources\Exception\BadRequestApiHttpException;
use Drupal\bw_rest_resources\Service\SsePusherService;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\KeyValueStore\KeyValueStoreExpirableInterface;
use Drupal\Core\Site\Settings;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\rest\Attribute\RestResource;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use OpenAI\Responses\Responses\RetrieveResponse;
use StandardWebhooks\Exception\WebhookSigningException;
use StandardWebhooks\Exception\WebhookVerificationException;
use StandardWebhooks\Webhook;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;

/**
 * OpenAI webhook resource for response event type.
 */
#[RestResource(
  id: "bw_openai_webhook_response",
  label: new TranslatableMarkup("BW: OpenAI webhook response"),
  uri_paths: [
    "create" => "/api/{version}/openai/webhook/response",
  ]
)]
final class OpenAiWebhookResponseResource extends ResourceBase {

  /**
   * The OpenAI service.
   *
   * @var \Drupal\bw_openai\Service\OpenAiService
   */
  protected OpenAiService $openAiService;

  /**
   * The Firebase remote config client.
   *
   * @var \Drupal\bw_firebase\Service\FirebaseRemoteConfigClient
   */
  protected FirebaseRemoteConfigClient $firebaseRemoteConfigClient;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * The job task repository.
   *
   * @var \Drupal\bw_job_task\Service\JobTaskRepository
   */
  protected JobTaskRepository $jobTaskRepository;

  /**
   * The key value expirable.
   *
   * @var \Drupal\Core\KeyValueStore\KeyValueStoreExpirableInterface
   */
  protected KeyValueStoreExpirableInterface $keyValueExpirable;

  /**
   * The SSE pusher service.
   *
   * @var \Drupal\bw_rest_resources\Service\SsePusherService
   */
  protected SsePusherService $sse;

  /**
   * The supported job types.
   *
   * @var string[]
   */
  protected $supportedJobTypes = [
    'ai_cv_analyser',
  ];

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $instance = parent::create($container, $configuration, $plugin_id, $plugin_definition);
    $instance->openAiService = $container->get('bw_openai.openai_service');
    $instance->firebaseRemoteConfigClient = $container->get('bw_firebase.client');
    $instance->entityTypeManager = $container->get('entity_type.manager');
    $instance->jobTaskRepository = $container->get('bw_job_task.repository');
    $instance->keyValueExpirable = $container->get('keyvalue.expirable')->get('ai_cv_analyser_background_jobs');
    $instance->sse = $container->get('bw_rest_resources.sse_pusher');
    return $instance;
  }

  /**
   * {@inheritdoc}
   *
   * Verify the webhook and process the response.
   */
  public function post(Request $request): ModifiedResourceResponse {
    $payload = $request->getContent() ?? '';

    if (empty($payload)) {
      $this->logger->error('Invalid request data: @payload', ['@payload' => $payload]);
      throw new BadRequestApiHttpException('Invalid request data.', 'Invalid request data.', 'AI_CV_ANALYSER_INVALID_REQUEST_DATA');
    }

    try {
      $webhook = new Webhook(Settings::get('openai.webhook_secret'));
      $webhook_headers = [
        'webhook-id' => $request->headers->get('webhook-id'),
        'webhook-timestamp' => $request->headers->get('webhook-timestamp'),
        'webhook-signature' => $request->headers->get('webhook-signature'),
      ];
      $event = $webhook->verify($payload, $webhook_headers);
      $response_id = $event['data']['id'] ?? '';
      $job_id = $this->keyValueExpirable->get($response_id);

      if (empty($job_id) || empty($response_id)) {
        $this->logger->error('Invalid response id: @response_id or job id: @job_id, from event: @event', [
          '@response_id' => $response_id,
          '@job_id' => $job_id,
          '@event' => json_encode($event),
        ]);
        throw new BadRequestApiHttpException('Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      }

      $job = $this->jobTaskRepository->get($job_id);
      if (!$job || !in_array($job->job_type, $this->supportedJobTypes)) {
        $this->logger->error('Invalid job type for response id: @response_id and job id: @job_id, from event: @event', [
          '@response_id' => $response_id,
          '@job_id' => $job_id,
          '@event' => json_encode($event),
        ]);
        throw new BadRequestApiHttpException('Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      }

      // Mark the job as running.
      $this->jobTaskRepository->markRunning($job_id);

      if ($event['type'] !== 'response.completed') {
        $this->logger->error('Failed response type for response id: @response_id and job id: @job_id, from event: @event', [
          '@response_id' => $response_id,
          '@job_id' => $job_id,
          '@event' => json_encode($event),
        ]);
        $this->failJob($job, 'Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
        throw new BadRequestApiHttpException('Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      }

      $response = $this->openAiService->getClient()->responses()->retrieve($response_id);

      $data = [];
      switch ($job->job_type) {
        case 'ai_cv_analyser':
          $data = $this->processAiCvAnalyserResponse($job, $response);
          break;
      }

      // Mark the job as done.
      $job = $this->jobTaskRepository->appendResultAndIncrement($job_id, $data);
      if ($this->jobTaskRepository->completeIfAllDone($job_id)) {
        $job = $this->jobTaskRepository->get($job_id);
        $this->sse->push($job->job_type, $job->uid, $job_id, [
          'type' => 'completed',
          'results' => $data,
        ]);
      }
    }
    catch (WebhookVerificationException $e) {
      $this->logger->error('Invalid webhook signature verification: @message', ['@message' => $e->getMessage()]);
      throw new BadRequestApiHttpException('Invalid webhook signature verification.', 'Invalid webhook signature verification.', 'AI_CV_ANALYSER_INVALID_WEBHOOK_SIGNATURE_VERIFICATION');
    }
    catch (WebhookSigningException $e) {
      $this->logger->error('Invalid webhook signature signing: @message', ['@message' => $e->getMessage()]);
      throw new BadRequestApiHttpException('Invalid webhook signature signing.', 'Invalid webhook signature signing.', 'AI_CV_ANALYSER_INVALID_WEBHOOK_SIGNATURE_SIGNING');
    }
    catch (BadRequestApiHttpException $e) {
      return new ModifiedResourceResponse();
    }
    catch (\Exception $e) {
      $this->logger->error('Error analyzing CV: @message', ['@message' => $e->getMessage()]);
      if ($job) {
        $this->failJob($job, 'System error, please contact the site administrator.', 'Connection failed. Please try again.', 'SYSTEM_ERROR');
      }
      throw new HttpException(500, 'Error analyzing CV.');
    }

    return new ModifiedResourceResponse();
  }

  /**
   * Fail the job.
   *
   * @param object $job
   *   The job.
   * @param string $message
   *   The error message.
   * @param string $safe_message
   *   The safe message.
   * @param string $error_code
   *   The error code.
   */
  private function failJob(object $job, string $message = '', string $safe_message = '', string $error_code = ''): void {
    $message = $this->jobTaskRepository->buildErrorMsg($message, $safe_message, $error_code);
    $this->jobTaskRepository->incrementFailed($job->job_id, $message);
    $this->jobTaskRepository->failIfAnyFailed($job->job_id);
    $this->sse->push($job->job_type, $job->uid, $job->job_id, ['type' => 'failed', 'error_msg' => $message]);
  }

  /**
   * Process the AI CV analyser response.
   *
   * @param object $job
   *   The job.
   * @param \OpenAI\Responses\Responses\RetrieveResponse $response
   *   The response.
   *
   * @return array
   *   The payload data.
   */
  private function processAiCvAnalyserResponse(object $job, RetrieveResponse $response): array {
    $json = $response->outputText ?? '';
    $data = json_decode($json, TRUE) ?? [];

    if (json_last_error() !== JSON_ERROR_NONE) {
      $this->logger->error('Error performing AI CV analyzer: @message', ['@message' => 'Model output was not valid JSON: ' . json_last_error_msg() . "\n\n" . $json]);
      $this->failJob($job, 'Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      throw new BadRequestApiHttpException('Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
    }

    $meta = json_decode($job->meta, TRUE) ?? [];
    // Clean up to avoid storing files forever.
    if (!empty($meta['uploaded_file_id'])) {
      $this->openAiService->getClient()->files()->delete(file: $meta['uploaded_file_id']);
    }

    // Check the status.
    if (empty($data['status']) || $data['status'] !== 'success') {
      $this->logger->error('Error performing AI CV analyzer: @message', ['@message' => 'Model output was not successful: ' . json_encode($data)]);
      $this->failJob($job, 'Error performing AI CV analyzer.', $data['payload']['error']['message'] ?? 'Generation failed. Try solo, bright shot.', $data['payload']['error']['code'] ?? 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      throw new BadRequestApiHttpException('Error performing AI CV analyzer.', $data['payload']['error']['message'] ?? 'Generation failed. Try solo, bright shot.', $data['payload']['error']['code'] ?? 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
    }

    if (empty($data['payload'])) {
      $this->logger->error('Error performing AI CV analyzer: @message', ['@message' => 'Model output was not successful: ' . json_encode($data)]);
      $this->failJob($job, 'Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
      throw new BadRequestApiHttpException('Error performing AI CV analyzer.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_MODEL_OUTPUT_NOT_SUCCESSFUL');
    }

    $cv_profile = $this->entityTypeManager->getStorage('profile')
      ->loadByProperties([
        'type' => 'cv',
        'uid' => $job->uid,
      ]);
    /** @var \Drupal\profile\Entity\Profile $cv_profile */
    $cv_profile = reset($cv_profile);

    if (!$cv_profile) {
      /** @var \Drupal\profile\Entity\Profile $cv_profile */
      $cv_profile = $this->entityTypeManager->getStorage('profile')->create([
        'type' => 'cv',
        'uid' => $job->uid,
      ]);
    }

    $cv_profile->set('field_ai_cv_analyser', serialize($data['payload']));
    $cv_profile->save();

    return $data['payload'];
  }

}
