<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Drupal\bw_rest_resources\Exception\NotFoundApiHttpException;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\rest\Attribute\RestResource;

/**
 * Get the AI CV analyser job task status (SSE).
 */
#[RestResource(
  id: "bw_user_ai_cv_analyser_job_task_events",
  label: new TranslatableMarkup("BW: User AI CV analyser job task events"),
  uri_paths: [
    "canonical" => "/api/{version}/user/ai-cv-analyser/events/{job_id}",
  ]
)]
final class UserAiCvAnalyserJobTaskEventsResource extends EventsStreamResourceBase {

  /**
   * {@inheritdoc}
   */
  protected function getJobType(): string {
    return 'ai_cv_analyser';
  }

  /**
   * {@inheritdoc}
   */
  protected function throwJobNotFoundException(): NotFoundApiHttpException {
    return new NotFoundApiHttpException('The AI CV analyser job could not be found.', 'Generation failed. Try solo, bright shot.', 'AI_CV_ANALYSER_JOB_NOT_FOUND');
  }

}
