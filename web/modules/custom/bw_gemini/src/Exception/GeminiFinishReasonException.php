<?php

namespace Drupal\bw_gemini\Exception;

/**
 * Exception thrown if Gemini finish reason is not handled.
 */
class GeminiFinishReasonException extends \Exception {

  /**
   * The error code.
   *
   * @var string
   */
  private string $errorCode;

  /**
   * The safe message.
   *
   * @var string
   */
  private string $safeMessage;

  public function __construct(string $message = '', string $safe_message = '', string $error_code = '', ?\Throwable $previous = NULL, int $code = 0) {
    $this->errorCode = $error_code;
    $this->safeMessage = $safe_message;

    parent::__construct($message, $code, $previous);
  }

  /**
   * Convert the exception data to an array.
   */
  public function toArray(): array {
    return [
      'status' => 'error',
      'code' => $this->errorCode,
      'message' => $this->message,
      'safe_message' => $this->safeMessage,
    ];
  }

}
