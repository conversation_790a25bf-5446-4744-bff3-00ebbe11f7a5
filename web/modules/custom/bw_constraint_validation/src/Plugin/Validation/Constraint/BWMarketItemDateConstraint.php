<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;

/**
 * Constrain for validation of the market item dates.
 *
 * @Constraint(
 *   id = "BWMarketItemDateConstraint",
 *   label = @Translation("BW market item date constraint", context = "Validation"),
 *   type = "string"
 * )
 */
final class BWMarketItemDateConstraint extends Constraint {

  /**
   * Constraint message for activation before expiration.
   */
  public string $activationBeforeExpirationMessage = 'Activation date (@act) must be before expiration date (@exp).';

  /**
   * Constraint message for past activation date.
   */
  public string $pastActivationDateMessage = 'Activation date (@act) is in the past.';

  /**
   * Constraint message for past expiration date.
   */
  public string $pastExpirationDateMessage = 'Expiration date (@exp) is in the past.';

}
