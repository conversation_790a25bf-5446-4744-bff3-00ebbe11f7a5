<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;

/**
 * Constrain for validation of the color fields.
 *
 * @Constraint(
 *   id = "BWColorConstraint",
 *   label = @Translation("BW color constraint", context = "Validation"),
 *   type = "string"
 * )
 */
final class BWColorConstraint extends Constraint {

  /**
   * Constraint message.
   */
  public string $message = 'The color is not valid.';

}
