<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Validates the BWMarketBlockImageTitleConstraintValidator constraint.
 */
final class BWMarketBlockImageTitleConstraintValidator extends ConstraintValidator {

  /**
   * {@inheritdoc}
   */
  public function validate($entity, Constraint $constraint) {
    if ($entity->bundle() !== 'market_block' || $entity->get('field_title_type')->value !== 'image') {
      return;
    }

    if ($entity->get('field_title_image')->isEmpty()) {
      $this->context
        ->buildViolation($constraint->message)
        ->atPath('field_title_image')
        ->addViolation();
    }
  }

}
