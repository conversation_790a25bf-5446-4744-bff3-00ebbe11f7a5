<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Validates the BWColorConstraint constraint.
 */
final class BWColorConstraintValidator extends ConstraintValidator {

  /**
   * {@inheritdoc}
   */
  public function validate($entity, Constraint $constraint) {
    if ($entity->bundle() !== 'market_block_title') {
      return;
    }

    $color = $entity->get('field_hex_color')->value;

    if (!$color) {
      return;
    }

    if (!preg_match('/^(\#[\da-f]{3}|\#[\da-f]{6})$/', $color)) {
      $this->context
        ->buildViolation($constraint->message)
        ->atPath('field_hex_color.0.value')
        ->addViolation();
    }
  }

}
