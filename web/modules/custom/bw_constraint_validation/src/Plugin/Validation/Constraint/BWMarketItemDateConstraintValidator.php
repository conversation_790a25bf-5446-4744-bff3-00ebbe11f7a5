<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Drupal\Core\Datetime\DrupalDateTime;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Validates the BWMarketItemDateConstraint constraint.
 */
final class BWMarketItemDateConstraintValidator extends ConstraintValidator {

  /**
   * {@inheritdoc}
   */
  public function validate($entity, Constraint $constraint) {
    if ($entity->bundle() !== 'market_item') {
      return;
    }

    $activation = $entity->get('field_activation_date')->value;
    $expiration = $entity->get('field_expiration_date')->value;

    if (!$activation || !$expiration) {
      return;
    }

    try {
      $act = new DrupalDateTime($activation);
      $exp = new DrupalDateTime($expiration);
      $now = new DrupalDateTime();
    }
    catch (\Exception $e) {
      // Let core widget validation handle bad formats.
      return;
    }

    if ($act->getTimestamp() < $now->getTimestamp()) {
      $this->context
        ->buildViolation($constraint->pastActivationDateMessage)
        ->setParameter('@act', $act->format('Y-m-d H:i:s'))
        ->atPath('field_activation_date.0.value')
        ->addViolation();
    }

    if ($exp->getTimestamp() < $now->getTimestamp()) {
      $this->context
        ->buildViolation($constraint->pastExpirationDateMessage)
        ->setParameter('@exp', $exp->format('Y-m-d H:i:s'))
        ->atPath('field_expiration_date.0.value')
        ->addViolation();
    }

    if ($act->getTimestamp() >= $exp->getTimestamp()) {
      $this->context
        ->buildViolation($constraint->activationBeforeExpirationMessage)
        ->setParameter('@act', $act->format('Y-m-d H:i:s'))
        ->setParameter('@exp', $exp->format('Y-m-d H:i:s'))
        // Point at one or both fields – here we point at expiration.
        ->atPath('field_expiration_date.0.value')
        ->addViolation();
    }
  }

}
