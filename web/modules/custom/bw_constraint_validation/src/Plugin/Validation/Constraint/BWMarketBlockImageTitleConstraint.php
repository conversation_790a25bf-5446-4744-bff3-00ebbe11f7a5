<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;

/**
 * Constrain for validation of the market block image title.
 *
 * @Constraint(
 *   id = "BWMarketBlockImageTitleConstraint",
 *   label = @Translation("BW market block image title constraint", context = "Validation"),
 *   type = "string"
 * )
 */
final class BWMarketBlockImageTitleConstraint extends Constraint {

  /**
   * Constraint message.
   */
  public string $message = 'The image is required for the title type "Image".';

}
